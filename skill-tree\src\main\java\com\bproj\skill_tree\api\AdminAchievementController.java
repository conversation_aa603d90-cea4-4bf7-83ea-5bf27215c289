package com.bproj.skill_tree.api;

import org.bson.types.ObjectId;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bproj.skill_tree.model.Achievement;
import com.bproj.skill_tree.service.AchievementService;
import java.util.List;

@RestController
@RequestMapping("api/admin/achievements")
public class AdminAchievementController {
	private final AchievementService achievementService;
	
	public AdminAchievementController(AchievementService achievementService) {
		this.achievementService = achievementService;
	}
	
	// list all achievements
	@GetMapping
	public ResponseEntity<List<Achievement>> findAll(Authentication auth) {
		List<Achievement> achievements = achievementService.findAll();
		return new ResponseEntity<>(achievements, HttpStatus.OK);
	}
	
	// get skill by id
	@GetMapping("/{achievementId}")
	public ResponseEntity<Achievement> findById(@PathVariable String achievementId) {
		try {
			ObjectId objectId = new ObjectId(achievementId);
			return achievementService.findById(objectId)
				.map(achievement -> new ResponseEntity<>(achievement, HttpStatus.OK))
				.orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
	
	// find by skillid
	@GetMapping("/skill/{skillId}")
	public ResponseEntity<List<Achievement>> findBySkillId(@PathVariable String skillId) {
		try {
			ObjectId objectId = new ObjectId(skillId);
			List<Achievement> achievements = achievementService.findBySkillId(objectId);
			return new ResponseEntity<>(achievements, HttpStatus.OK);
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
	
	// delete achievement by id
	@DeleteMapping("/{achievementId}")
	public ResponseEntity<Void> deleteById(@PathVariable String achievementId) {
		try {
			ObjectId objectId = new ObjectId(achievementId);
			if (achievementService.existsById(objectId)) {
				achievementService.deleteById(objectId);
				return new ResponseEntity<>(HttpStatus.OK);
			}
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
	
	// delete by skill id
	@DeleteMapping("/skill/{skillId}")
	public ResponseEntity<Void> deleteBySkillId(@PathVariable String skillId) {
		try {
			ObjectId objectId = new ObjectId(skillId);
			achievementService.deleteBySkillId(objectId);
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
}
