package com.bproj.skill_tree.service;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.bproj.skill_tree.dao.SkillRepository;
import com.bproj.skill_tree.model.Skill;

import java.util.List;
import java.util.Optional;

@Service
public class SkillService {
	private final SkillRepository skillRepository;
	
	@Autowired
	public SkillService(@Qualifier("mongoSkillRepository") SkillRepository skillRepository) {
		this.skillRepository = skillRepository;
	}
	
	public Skill create(Skill skill) {
		return skillRepository.insert(skill);
	}
	
	public boolean existsById(ObjectId skillId) {
		return skillRepository.existsById(skillId);
	}
	
	public Optional<Skill> findById(ObjectId skillId) {
		return skillRepository.findById(skillId);
	}
	
	public List<Skill> findAll() {
		return skillRepository.findAll();
	}
	
	public List<Skill> findByUserId(ObjectId userId) {
		return skillRepository.findByUserId(userId);
	}
	
	public List<Skill> findByUserIdAndParentSkillId(ObjectId userId, ObjectId parentSkillId) {
		return skillRepository.findByUserIdAndParentSkillId(userId, parentSkillId);
	}
	
	public List<Skill> findRootSkillsByUserId(ObjectId userId) {
		return skillRepository.findByUserIdAndParentSkillIdIsNull(userId);
	}
	
	public List<Skill> findByName(String name) {
		return skillRepository.findByName(name);
	}
	
	public List<Skill> findByUserIdAndName(ObjectId userId, String name) {
		return skillRepository.findByUserIdAndName(userId, name);
	}
	
	public Skill update(Skill updatedSkill) {
		return skillRepository.save(updatedSkill);
	}
	
	public void deleteById(ObjectId skillId) {
		skillRepository.deleteById(skillId);
	}
	
	public void deleteByUserId(ObjectId userId) {
		List<Skill> userSkills = findByUserId(userId);
		skillRepository.deleteAll(userSkills);
	}
}