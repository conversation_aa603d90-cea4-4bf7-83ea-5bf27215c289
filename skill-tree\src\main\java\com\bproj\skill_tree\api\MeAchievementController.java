package com.bproj.skill_tree.api;

import org.apache.tomcat.util.http.parser.Authorization;
import org.bson.types.ObjectId;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bproj.skill_tree.model.Achievement;
import com.bproj.skill_tree.service.AchievementService;
import com.bproj.skill_tree.service.UserService;

import java.util.List;
import java.util.NoSuchElementException;

@RestController
@RequestMapping("/api/me/achievements")
public class MeAchievementController {
	private final AchievementService achievementService;
	private final UserService userService;
	
	public MeAchievementController(AchievementService achievementService, UserService userService) {
		this.achievementService = achievementService;
		this.userService = userService;
	}
	
	private ObjectId getUserIdByFirebaseId(String firebaseId) {
		try {
			ObjectId userId = userService.findByFirebaseId(firebaseId).get().getId();
			return userId;
		} catch (NoSuchElementException nsee) {
			return null;
		}
	}
	
	// create an achievement for this user
	@PostMapping
	public ResponseEntity<Achievement> create(Authentication auth, @RequestBody Achievement achievement) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if (userId == null) {
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}
		achievement.setUserId(userId);
		Achievement createdAchievement = achievementService.create(achievement);
		return createdAchievement != null
			? new ResponseEntity<>(createdAchievement, HttpStatus.CREATED)
			: new ResponseEntity<>(HttpStatus.BAD_REQUEST);
	}
	
	// get achievements for this user
	@GetMapping
	public ResponseEntity<List<Achievement>> findByUserId(Authentication auth) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if(userId == null) {
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}
		try {
			List<Achievement> achievements = achievementService.findByUserId(userId);
			return new ResponseEntity<>(achievements, HttpStatus.OK);
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
	
	// find by user and skill id
	@GetMapping("/skill/{skillId}")
	public ResponseEntity<List<Achievement>> findByUserIdAndSkillId(
			Authentication auth, 
			@PathVariable String skillId) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if (userId == null) {
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}
		try {
			ObjectId skillObjectId = new ObjectId(skillId);
			List<Achievement> achievements = achievementService.findByUserIdAndSkillId(userId, skillObjectId);
			return new ResponseEntity<>(achievements, HttpStatus.OK);
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
	
	// update user's achievement
	@PutMapping
	public ResponseEntity<Achievement> update(
			Authentication auth,
			@RequestBody Achievement updatedAchievement) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if (userId == null) {
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}
		updatedAchievement.setUserId(userId);
		Achievement result = achievementService.update(updatedAchievement);
		return new ResponseEntity<>(result, HttpStatus.OK);
	}
	
	// delete single user achievement
	@DeleteMapping("/{achievementId}")
	public ResponseEntity<Void> deleteByUserIdAndAchievementid(
			Authentication auth,
			@PathVariable String achievementId) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if (userId == null) {
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}
		ObjectId achievementObjectId = new ObjectId(achievementId);
		if (achievementService.existsById(achievementObjectId)) {
			achievementService.deleteById(achievementObjectId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		
	}
	
	
	// delete all user achievements
	@DeleteMapping
	public ResponseEntity<Void> deleteByUserId(Authentication auth) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		
		if(userId == null) {
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}
		try {
			achievementService.deleteByUserId(userId);
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
}
