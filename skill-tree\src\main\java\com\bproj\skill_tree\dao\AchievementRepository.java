package com.bproj.skill_tree.dao;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.bproj.skill_tree.model.Achievement;

@Repository("mongoAchievementRepository")
public interface AchievementRepository extends MongoRepository<Achievement, ObjectId>{
    List<Achievement> findByUserId(ObjectId userId);
    List<Achievement> findBySkillId(ObjectId skillId);
    List<Achievement> findByUserIdAndSkillId(ObjectId userId, ObjectId skillId);
    List<Achievement> findByUserIdAndIsComplete(ObjectId userId, boolean isComplete);
    List<Achievement> findBySkillIdAndIsComplete(ObjectId skillId, boolean isComplete);
    List<Achievement> findByTitle(String title);
    List<Achievement> findByUserIdAndTitle(ObjectId userId, String title);
}