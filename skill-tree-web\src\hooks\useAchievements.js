import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { AchievementsService } from "@/services/achievements.service";

export function useAchievements(skillId) {
    return useQuery({
        queryKey: ["achievements", { skillId }],
        queryFn: () => AchievementsService.listBySkill(skillId),
        enabled: !!skillId,
    });
}

export function useCreateAchievement(skillId) {
    const qc = useQueryClient();
    return useMutation({
        mutationFn: (payload) => AchievementsService.create(skillId, payload),
        onSuccess: () =>
            qc.invalidateQueries({ queryKey: ["achievements", { skillId }] }),
    });
}

export function useUpdateAchievement(skillId) {
    const qc = useQueryClient();
    return useMutation({
        mutationFn: ({ id, payload }) => AchievementsService.update(id, payload),
        onSuccess: () =>
            qc.invalidateQueries({ queryKey: ["achievements", { skillId }] }),
    });
}

export function useDeleteAchievement(skillId) {
    const qc = useQueryClient();
    return useMutation({
        mutationFn: (id) => AchievementsService.remove(id),
        onSuccess: () =>
            qc.invalidateQueries({ queryKey: ["achievements", { skillId }] }),
    });
}
