/* Backdrop covers entire screen */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

/* Modal box */
.modal {
    background: #fff;
    border-radius: 12px;
    padding: 24px 32px;
    width: 420px;
    max-width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.2s ease-out;
}

/* Title */
.modal h2 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 1.4rem;
    color: #333;
}

/* Form layout */
.modal form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.modal label {
    display: flex;
    flex-direction: column;
    font-size: 0.9rem;
    color: #444;
}

.modal input[type="text"],
.modal textarea,
.modal select {
    margin-top: 6px;
    padding: 8px;
    font-size: 0.9rem;
    border: 1px solid #ccc;
    border-radius: 6px;
}

.modal textarea {
    min-height: 80px;
    resize: vertical;
}

/* Helper text */
.modal small {
    font-size: 0.8rem;
    color: #666;
    margin-top: 4px;
}

/* Action buttons */
.actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 8px;
}

.actions button {
    padding: 8px 14px;
    font-size: 0.9rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.actions button[type="button"] {
    background: #eee;
    color: #333;
}

.actions button[type="button"]:hover {
    background: #ddd;
}

.actions button[type="submit"] {
    background: #28a745;
    color: white;
}

.actions button[type="submit"]:hover {
    background: #218838;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}