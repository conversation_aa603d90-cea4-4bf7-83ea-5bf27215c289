import { getAuth } from "firebase/auth";

export function attachAuthInterceptor(api) {
    // Add Authorization header
    api.interceptors.request.use(async (config) => {
        const token = await getAuth().currentUser?.getIdToken?.();
        if (token) config.headers.Authorization = `Bearer ${token}`;
        return config;
    });

    // Handle 401s (example: just log; you can add refresh or sign-out logic)
    api.interceptors.response.use(
        (r) => r,
        async (error) => {
            if (error?.response?.status === 401) {
                console.warn("Unauthorized. Consider refreshing token or redirecting to login.");
            }
            return Promise.reject(error);
        }
    );
}

export function attachLoggingInterceptor(api) {
    api.interceptors.response.use(
        (r) => r,
        (error) => {
            // central place to map errors to a uniform shape if you want
            const status = error?.response?.status;
            const message = error?.response?.data?.message || error.message;
            console.error(`[API ${status}]`, message);
            return Promise.reject(error);
        }
    );
}
