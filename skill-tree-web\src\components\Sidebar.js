import React from "react";
import "./SkillTreePage.css";
import { auth } from "../firebase";
import { signOut } from "firebase/auth";
import { useNavigate } from "react-router-dom";

export default function Sidebar({ items = [], onSignOut }) {
    return (
        <div className="sidebar">
            {items.map(({ label, onClick }, i) => (
                <button key={i} className="sidebar-button" onClick={onClick}>
                    {label}
                </button>
            ))}
        </div>
    );
}