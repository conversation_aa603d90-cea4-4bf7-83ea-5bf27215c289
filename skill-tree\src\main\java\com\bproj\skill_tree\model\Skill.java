package com.bproj.skill_tree.model;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonProperty;

@Document(collection = "skills")
public class Skill {
	@Id
	private ObjectId id;
	private ObjectId userId;
	private String name;
	private RatingType ratingType;
	private Object ratingValue;
	private List<String> ratingOptions;
	private double timeSpentHours;
	private ObjectId parentSkillId;
	@CreatedDate
	private Instant createdAt;
	@LastModifiedDate
	private Instant updatedAt;
	
	
	public Skill() {}
	
	public Skill(@JsonProperty("_id") ObjectId id,
				 @JsonProperty("user_id") ObjectId userId,
				 @JsonProperty("name") String name,
				 @JsonProperty("rating_type") RatingType ratingType,
				 @JsonProperty("rating_value") Object ratingValue,
				 @JsonProperty("rating_options") List<String> ratingOptions,
				 @JsonProperty("time_spent_hours") double timeSpentHours,
				 @JsonProperty("parent_skill_id") ObjectId parentSkillId,
				 @JsonProperty("created_at") Instant createdAt,
				 @JsonProperty("updated_at") Instant updatedAt){
		this.id = id;
		this.userId = userId;
		this.name = name;
		this.ratingType = ratingType;
		this.ratingValue = ratingValue;
		this.ratingOptions = ratingOptions != null ? ratingOptions : new ArrayList<String>();
		this.timeSpentHours = timeSpentHours;
		this.parentSkillId = parentSkillId;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
	}

	public ObjectId getSkillId() {
		return id;
	}

	public void setSkillId(ObjectId skillId) {
		this.id = skillId;
	}

	public ObjectId getUserId() {
		return userId;
	}

	public void setUserId(ObjectId userId) {
		this.userId = userId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public RatingType getRatingType() {
		return ratingType;
	}

	public void setRatingType(RatingType ratingType) {
		this.ratingType = ratingType;
	}

	public Object getRatingValue() {
		return ratingValue;
	}

	public void setRatingValue(Object ratingValue) {
		this.ratingValue = ratingValue;
	}

	public List<String> getRatingOptions() {
		return ratingOptions;
	}

	public void setRatingOptions(List<String> ratingOptions) {
		this.ratingOptions = ratingOptions;
	}

	public double getTimeSpentHours() {
		return timeSpentHours;
	}

	public void setTimeSpentHours(double timeSpentHours) {
		this.timeSpentHours = timeSpentHours;
	}

	public ObjectId getParentSkillId() {
		return parentSkillId;
	}

	public void setParentSkillId(ObjectId parentSkillId) {
		this.parentSkillId = parentSkillId;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}
	
}
