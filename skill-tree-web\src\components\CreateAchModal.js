import { useState, useMemo, useEffect } from "react";
import './CreateAchModal.css'

export default function CreateAchModal({
    isOpen,
    onClose,
    onSubmit,
    rootSkills,
    otherAchievements }) {

    const initialForm = {
        title: "",
        rootSkill: 0,
        description: "",
        prerequisites: [],
    };

    const [form, setForm] = useState({
        title: "",
        rootSkill: 0,
        description: "",
        prerequisites: [],
    });

    useEffect(() => {
        if (isOpen) {
            setForm((f) => ({ ...f }));
        }
    }, [isOpen]);

    const skillOptions = useMemo(() => rootSkills, [rootSkills]);
    const achievementOptions = useMemo(() => otherAchievements, [otherAchievements]);
    const update = (p) => setForm((f) => ({ ...f, ...p }));

    const handleChange = (e) => {
        const { name, value } = e.target;
        update({ [name]: value });
    };

    const handleParentsChange = (e) => {
        const selected = Array.from(e.target.rootSkills).map((o) => o.value);
        update({ parents: selected })
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        // TODO: check for invalid forms, return if invalid (see createskillmodal)
        onSubmit(form);
        setForm(initialForm);
        onClose();
    }

    if (!isOpen) return null;
    return (
        <div className="modal-backdrop">
            <div className="modal">
                <h2>Create Skill</h2>
                <form onSubmit={handleSubmit}>
                    <label>
                        Achievement title
                        <input
                            name="title"
                            value={form.title}
                            onChange={handleChange}
                            placeholder="e.g., Portfolio Website"
                            required
                        />
                    </label>
                    <label>
                        Associated Skill
                        <select
                            value={form.rootSkill}
                            onChange={handleChange}
                            required
                        >
                            {skillOptions.map((s) => (
                                <option key={s.id} value={s.id}>
                                    {s.name}
                                </option>
                            ))};
                        </select>
                    </label>
                    <label>
                        Description
                        <input
                            name="description"
                            type="text"
                            value={form.description}
                            onChange={handleChange}
                            placeholder="e.g., Create a publicly available website with your accomplishments/qualifications."
                        />
                    </label>
                    <label>
                        Prerequisites
                        <select
                            multiple
                            value={form.prerequisites}
                            onChange={handleChange}
                        >
                            {achievementOptions.map((a) => (
                                <option key={a.id} value={a.id}>
                                    {a.name}
                                </option>
                            ))};
                        </select>
                        <small>Hold Ctrl/Cmd to select multiple.</small>
                    </label>
                    <div className="actions">
                        <button type="button" onClick={onClose}>Cancel</button>
                        <button type="submit">Create</button>
                    </div>
                </form>
            </div>

        </div>
    );
}
