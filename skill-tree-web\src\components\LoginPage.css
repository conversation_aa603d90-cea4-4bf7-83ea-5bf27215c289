.login-page {
  min-height: 100vh;
  background-color: #4A4A4A;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Arial, sans-serif;
}

.login-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.input-label {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.input-button-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-input {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  color: #333;
  min-width: 200px;
}

.user-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #20B2AA;
}

.enter-button {
  background-color: #20B2AA;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.enter-button:hover {
  background-color: #1a8f8a;
}

.login-instruction {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
} 