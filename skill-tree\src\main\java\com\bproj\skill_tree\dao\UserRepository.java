package com.bproj.skill_tree.dao;

import java.util.Optional;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.bproj.skill_tree.model.User;

@Repository("mongoUserRepository")
public interface UserRepository extends MongoRepository<User, ObjectId>{
    Optional<User> findByFirebaseId(String firebaseId);
    Optional<User> findByEmail(String email);
}
