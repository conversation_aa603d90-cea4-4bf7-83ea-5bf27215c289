package com.bproj.skill_tree.api;

import org.bson.types.ObjectId;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bproj.skill_tree.model.Skill;
import com.bproj.skill_tree.model.User;
import com.bproj.skill_tree.service.SkillService;
import com.bproj.skill_tree.service.UserService;
import java.util.List;
import java.util.NoSuchElementException;

@RestController
@RequestMapping("api/me/skills")
public class MeSkillController {
	private final SkillService skillService;
	private final UserService userService;
	
	public MeSkillController(SkillService skillService, UserService userService) {
		this.skillService = skillService;
		this.userService = userService;
	}
	
	private ObjectId getUserIdByFirebaseId(String firebaseId) {
		try {
			ObjectId userId = userService.findByFirebaseId(firebaseId).get().getId();
			return userId;
		} catch (NoSuchElementException nsee) {
			return null;	
		}
	}
	// create a skill under this user
	@PostMapping
	public ResponseEntity<Skill> create(Authentication auth, @RequestBody Skill skill) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if (userId != null) {
			skill.setUserId(userId);
			Skill createdSkill = skillService.create(skill);
			return createdSkill != null 
				? new ResponseEntity<>(createdSkill, HttpStatus.CREATED)
				: new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
		return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
	
	// find this users skills
	@GetMapping
	public ResponseEntity<List<Skill>> findByUserId(Authentication auth) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
	    if(userId != null) {
	    	List<Skill> skills = skillService.findByUserId(userId);
	    	return new ResponseEntity<>(skills, HttpStatus.OK);
	    }
	    return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
	
	// find this users root skills
	@GetMapping("/root")
	public ResponseEntity<List<Skill>> findRootSkillsByUserId(Authentication auth) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if(userId != null) {
			List<Skill> rootSkills = skillService.findRootSkillsByUserId(userId);
			return new ResponseEntity<>(rootSkills, HttpStatus.OK);
		}
		return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
	
	// find subskills of a skill for this user
	@GetMapping("/parent/{parentSkillId}")
	public ResponseEntity<List<Skill>> findByUserIdAndParentSkillId(
			Authentication auth, 
			@PathVariable String parentSkillId) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		if (userId != null) {
			List<Skill> subSkills = skillService.findByUserIdAndParentSkillId(userId, new ObjectId(parentSkillId));
			return new ResponseEntity<>(subSkills, HttpStatus.OK);
		}
		return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
	
	// update a skill for this user
	@PutMapping
	public ResponseEntity<Skill> update(
			Authentication auth,
			@RequestBody Skill updatedSkill) {
		
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		
		if (userId != null) {
			updatedSkill.setUserId(userId);
			Skill result = skillService.update(updatedSkill);
			return result != null
				? new ResponseEntity<>(result, HttpStatus.OK)
				: new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		} 
		return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
	
	// delete this skill for this user
	@DeleteMapping("/{skillId}")
	public ResponseEntity<Void> deleteById(
			Authentication auth,
			@PathVariable String skillId) {
		
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		
		if (userId != null) {
			ObjectId skillIdObj = new ObjectId(skillId);
			if(skillService.existsById(skillIdObj)) {
				skillService.deleteById(skillIdObj);
				return new ResponseEntity<>(HttpStatus.OK);
			}
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		}
		return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
	
	// delete all of this user's skills
	@DeleteMapping("/all")
	public ResponseEntity<Void> deleteByUserId(Authentication auth) {
		String firebaseId = auth.getPrincipal().toString();
		ObjectId userId = getUserIdByFirebaseId(firebaseId);
		
		if (userId != null) {
			skillService.deleteByUserId(userId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
}
