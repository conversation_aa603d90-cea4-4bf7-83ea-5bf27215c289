import React from 'react';
import './SkillTreePage.css';
import { auth } from "../firebase";
import { signOut } from "firebase/auth";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from 'react-router-dom';
import Sidebar from "../components/Sidebar";
import { label } from 'three/tsl';
import Modal from "../components/Modal";
import CreateSkillModal from '../components/CreateSkillModal';
import CreateAchModal from './CreateAchModal';
import { useState } from 'react';


function SkillTreePage() {
  const navigate = useNavigate()

  const [showSkillModal, setShowSkillModal] = useState(false);
  const [showAchModal, setShowAchModal] = useState(false);

  const [skillName, setSkillName] = useState("");
  const [skillDesc, setSkillDesc] = useState("");

  const [pending, setPending] = useState(false);

  const handleSignOut = async () => {
    signOut(auth)
    navigate('/login')
    try {
      await signOut(auth);
    } catch (error) {
      alert(error.message);
      return
    }
  };

  const handleCreateSkill = () => {
    setShowSkillModal(true);
  };

  const handleCreateAchievement = () => {
    setShowAchModal(true);
  }

  // assume valid forms, make associated api requests
  const handleSkillCreation = (form) => {

  }

  const handleAchievementCreation = (form) => {

  }

  const sideBarItems = [
    { label: 'Create Skill', onClick: () => { handleCreateSkill() } },
    { label: 'Create Achievement', onClick: () => { handleCreateAchievement() } },
    { label: 'Button 3', onClick: () => { alert('button3') } },
    { label: 'Button 4', onClick: () => { alert('Button 4 clicked') } },
    { label: 'Sign Out', onClick: () => { handleSignOut() } }
  ];
  return (
    <div className="skill-tree-page">
      <div className="skill-tree-container">
        {/* Left sidebar with buttons */}
        <Sidebar items={sideBarItems} />

        {/* Main skill tree area */}
        <div className="tree-area">
        </div>
      </div>

      {/*skill modal*/}
      <CreateSkillModal isOpen={showSkillModal} onClose={() => setShowSkillModal(false)} onSubmit={handleSkillCreation} allSkills={[]} />

      {/*ach modal*/}
      <CreateAchModal isOpen={showAchModal} onClose={() => setShowAchModal(false)} onSubmit={handleAchievementCreation} rootSkills={[]} otherAchievements={[]} />
    </div>
  );
}

export default SkillTreePage; 