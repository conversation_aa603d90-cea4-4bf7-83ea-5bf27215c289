package com.bproj.skill_tree.security;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Component
@ConfigurationProperties(prefix = "app.admin")
@Getter @Setter
public class AdminProps {
	private List<String> uids = List.of();
	private List<String> emails = List.of();
	
	public List<String> getUids() {
		return uids;
	}
	
	public List<String> getEmails() {
		return emails;
	}
}
