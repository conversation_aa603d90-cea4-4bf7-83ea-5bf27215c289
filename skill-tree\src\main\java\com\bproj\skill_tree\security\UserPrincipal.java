package com.bproj.skill_tree.security;

import org.springframework.security.core.GrantedAuthority;
import java.security.Principal;
import java.util.Collection;
import java.util.List;

public class UserPrincipal implements Principal {
  private final String firebaseId;
  private final String email;
  private final Collection<? extends GrantedAuthority> authorities;

  public UserPrincipal(String firebaseId, String email,
                         Collection<? extends GrantedAuthority> authorities) {
    this.firebaseId = firebaseId;
    this.email = email;
    this.authorities = authorities == null ? List.of() : authorities;
  }

  public String getFirebaseId() { return firebaseId; }
  public String getEmail() { return email; }
  public Collection<? extends GrantedAuthority> getAuthorities() { return authorities; }
  @Override public String getName() { return firebaseId; }
  @Override public String toString() { return firebaseId; }
}
