package com.bproj.skill_tree.api;

import org.bson.types.ObjectId;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bproj.skill_tree.model.User;
import com.bproj.skill_tree.service.UserService;
import java.util.List;

@RestController
@RequestMapping("/api/admin/users")
public class AdminUserController {
	private final UserService userService;
	
	public AdminUserController(UserService userService) {
		this.userService = userService;
	}
	
	// Get all Users
	@GetMapping
	public ResponseEntity<List<User>> findAll() {
		List<User> users = userService.findAll();
		return new ResponseEntity<>(users, HttpStatus.OK);
	}
	
	// Get User by email
	@GetMapping("/email")
	public ResponseEntity<User> findByEmail(@RequestParam String email) {
		return userService.findByEmail(email)
			.map(user -> new ResponseEntity<>(user, HttpStatus.OK))
			.orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
	}
	
	// Delete user by id
	@DeleteMapping("/{userId}")
	public ResponseEntity<Void> deleteById(@PathVariable String userId) {
		try {
			ObjectId objectId = new ObjectId(userId);
			if (userService.existsById(objectId)) {
				userService.deleteById(objectId);
				return new ResponseEntity<>(HttpStatus.OK);
			}
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
}
