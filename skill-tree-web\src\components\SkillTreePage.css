.skill-tree-page {
  min-height: 100vh;
  background-color: #4A4A4A;
  font-family: Arial, sans-serif;
  position: relative;
}

.skill-tree-container {
  display: flex;
  height: 100vh;
}

/* Sidebar */
.sidebar {
  width: 120px;
  background-color: rgb(65, 74, 92);
  padding: 20px 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
}

.sidebar-button {
  background-color: #20B2AA;
  color: white;
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.sidebar-button:hover {
  background-color: #1a8f8a;
}

/* Tree area */
.tree-area {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Root node */
.root-node {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background-color: #20B2AA;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: black;
  font-weight: bold;
  font-size: 1.2rem;
  z-index: 10;
}

/* Skill nodes */
.skill-node {
  position: absolute;
  width: 80px;
  height: 80px;
  background-color: #20B2AA;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: black;
  font-weight: bold;
  font-size: 0.9rem;
  z-index: 10;
}

.skill-1 {
  top: 25%;
  left: 25%;
}

.skill-2 {
  top: 25%;
  left: 75%;
}

.skill-3 {
  top: 75%;
  left: 25%;
}

.skill-4 {
  top: 75%;
  left: 75%;
}

/* Child nodes */
.skill-children {
  position: absolute;
  display: flex;
  gap: 10px;
}

.child-node {
  width: 30px;
  height: 30px;
  background-color: #20B2AA;
  border-radius: 50%;
}

/* Child positions for Skill 1 */
.child-1-1 {
  position: absolute;
  top: -20px;
  left: -15px;
}

.child-1-2 {
  position: absolute;
  top: -40px;
  left: 0px;
}

.child-1-3 {
  position: absolute;
  top: -20px;
  left: 15px;
}

/* Child positions for Skill 2 */
.child-2-1 {
  position: absolute;
  top: -40px;
  left: 0px;
}

.child-2-2 {
  position: absolute;
  top: -40px;
  left: 15px;
}

.child-2-3 {
  position: absolute;
  top: -20px;
  left: 30px;
}

/* Child positions for Skill 3 */
.child-3-1 {
  position: absolute;
  top: 20px;
  left: -15px;
}

.child-3-2 {
  position: absolute;
  top: 40px;
  left: 0px;
}

.child-3-3 {
  position: absolute;
  top: 20px;
  left: 15px;
}

/* Child positions for Skill 4 */
.child-4-1 {
  position: absolute;
  top: 20px;
  left: -15px;
}

.child-4-2 {
  position: absolute;
  top: 40px;
  left: 0px;
}

.child-4-3 {
  position: absolute;
  top: 20px;
  left: 15px;
}

/* Connection lines */
.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  pointer-events: none;
}

/* modal shell */
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-window {
  background: #111;
  color: #fff;
  /* adjust to your theme */
  width: min(560px, 92vw);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  outline: none;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.modal-close {
  background: transparent;
  border: 0;
  font-size: 22px;
  cursor: pointer;
  color: inherit;
}

.modal-body {
  padding: 16px;
}

.field {
  display: grid;
  gap: 6px;
  margin-bottom: 12px;
}

.field input,
.field textarea {
  background: #1b1b1b;
  border: 1px solid #2a2a2a;
  color: #fff;
  border-radius: 8px;
  padding: 10px;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 8px;
}

.modal-actions button {
  background: #2b2b2b;
  border: 1px solid #3a3a3a;
  color: #fff;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
}

.modal-actions button[type="submit"] {
  background: #3e7bfa;
  border-color: #3e7bfa;
}

.modal-actions button:hover {
  opacity: 0.92;
}