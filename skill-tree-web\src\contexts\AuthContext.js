// example: src/contexts/AuthContext.js
import { createContext, useContext, useEffect, useState } from "react";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../firebase";

const AuthContext = createContext();
export function useAuth() { return useContext(AuthContext); }

export function AuthProvider({ children }) {
    const [user, setUser] = useState(null);
    useEffect(() => onAuthStateChanged(auth, setUser), []);
    return <AuthContext.Provider value={user}>{children}</AuthContext.Provider>;
}
