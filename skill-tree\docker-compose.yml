version: '3.8'
services:
  mongodb:
    image: mongo:6.0
    container_name: mongo
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db

  app:
    build: .
    image: my-springboot-app:latest
    depends_on:
      - mongodb
    ports:
      - "8080:8080"
    environment:
      # Tell Spring Boot where Mongo lives
      SPRING_DATA_MONGODB_URI: mongodb://mongodb:27017/mydatabase

volumes:
  mongo-data:
