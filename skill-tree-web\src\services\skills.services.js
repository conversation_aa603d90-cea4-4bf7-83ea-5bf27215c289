import { api } from "@/api/client";

export const SkillsService = {
    list: () => api.get("/skills").then((r) => r.data),
    get: (id) => api.get(`/skills/${id}`).then((r) => r.data),
    create: (payload) => api.post("/skills", payload).then((r) => r.data),
    update: (id, payload) => api.patch(`/skills/${id}`, payload).then((r) => r.data),
    remove: (id) => api.delete(`/skills/${id}`).then((r) => r.data),
};
