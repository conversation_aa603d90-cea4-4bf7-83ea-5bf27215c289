package com.bproj.skill_tree.security;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;

public class FirebaseAuthenticationFilter extends OncePerRequestFilter{
	private static final String HEADER = "Authorization";
	private static final String PREFIX = "Bearer ";
	@Autowired private AdminProps adminProps;
	
	private String extractBearer(HttpServletRequest req) {
		  String h = req.getHeader("Authorization");
		  if (h == null || !h.startsWith("Bearer ")) return null;
		  return h.substring(7);
	}
	
	@Override
	protected void doFilterInternal(HttpServletRequest req, HttpServletResponse res, FilterChain chain)
	    throws IOException, ServletException {

	  String idToken = extractBearer(req);
	  if (idToken != null) {
	    try {
	      FirebaseToken decoded = FirebaseAuth.getInstance().verifyIdToken(idToken);

	      boolean isAdmin =
	          adminProps.getUids().contains(decoded.getUid()) ||
	          (decoded.getEmail() != null && adminProps.getEmails()
	                .stream().anyMatch(e -> e.equalsIgnoreCase(decoded.getEmail())));

	      var authorities = new ArrayList<GrantedAuthority>();
	      authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
	      if (isAdmin) authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));

	      var principal = new UserPrincipal(decoded.getUid(), decoded.getEmail(), authorities);
	      SecurityContextHolder.getContext().setAuthentication(
	          new UsernamePasswordAuthenticationToken(principal, null, authorities));

	    } catch (FirebaseAuthException e) {
	      SecurityContextHolder.clearContext();
	      res.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid token");
	      return;
	    }
	  }
	  chain.doFilter(req, res);
	}
}
