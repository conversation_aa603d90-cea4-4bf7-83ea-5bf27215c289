package com.bproj.skill_tree.api;

import org.bson.types.ObjectId;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bproj.skill_tree.model.Achievement;
import com.bproj.skill_tree.service.AchievementService;
import java.util.List;

@RestController
@RequestMapping("/api/achievements")
public class AchievementController {
	private final AchievementService achievementService;
	
	public AchievementController(AchievementService achievementService) {
		this.achievementService = achievementService;
	}
}