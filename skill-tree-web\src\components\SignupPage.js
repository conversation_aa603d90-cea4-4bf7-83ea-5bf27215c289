import React from 'react';
import './SignupPage.css';
import { useNavigate } from 'react-router-dom';
import { createUserWithEmailAndPassword } from "firebase/auth";
import { getAuth } from "firebase/auth";
import { auth } from "../firebase";
function SignupPage() {


  const [email, setEmail] = React.useState('')
  const [password, setPassword] = React.useState('')
  const [confirmPassword, setConfirmPassword] = React.useState('')
  const navigate = useNavigate()

  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!emailRegex.test(email)) {
      alert('Invalid email')
      return;
    }
    if (password !== confirmPassword) {
      alert('Passwords do not match')
      return;
    }
    if (password.length < 8) {
      alert('Password must be at least 8 characters')
      return;
    }
    try {
      await createUserWithEmailAndPassword(auth, email, password)
      navigate('/login')
    } catch (error) {
      alert(error.message);
      return;
    }
  };
  return (
    <div className="signup-page">
      <div className="signup-content">
        <form onSubmit={handleSubmit} className="signup-form">
          <div className="input-container">
            <div className="input-button-group">
              <input
                type="text"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="user-input"
                placeholder="Enter Email"
              />
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="user-input"
                placeholder="Enter Password"
              />
              <input
                type="password"
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="user-input"
                placeholder="Confirm Password"
              />
              <button type="submit" className="enter-button">
                →
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default SignupPage; 