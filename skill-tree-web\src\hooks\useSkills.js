import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { SkillsService } from "@/services/skills.service";

export function useSkills() {
    return useQuery({
        queryKey: ["skills"],
        queryFn: SkillsService.list,
        staleTime: 60_000, // optional
    });
}

export function useSkill(id) {
    return useQuery({
        queryKey: ["skills", id],
        queryFn: () => SkillsService.get(id),
        enabled: !!id,
    });
}

export function useCreateSkill() {
    const qc = useQueryClient();
    return useMutation({
        mutationFn: SkillsService.create,
        onSuccess: () => qc.invalidateQueries({ queryKey: ["skills"] }),
    });
}

export function useUpdateSkill() {
    const qc = useQueryClient();
    return useMutation({
        mutationFn: ({ id, payload }) => SkillsService.update(id, payload),
        onSuccess: (_, { id }) => {
            qc.invalidateQueries({ queryKey: ["skills"] });
            qc.invalidateQueries({ queryKey: ["skills", id] });
        },
    });
}

export function useDeleteSkill() {
    const qc = useQueryClient();
    return useMutation({
        mutationFn: SkillsService.remove,
        onSuccess: () => qc.invalidateQueries({ queryKey: ["skills"] }),
    });
}
