package com.bproj.skill_tree.model;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.ArrayList;

@Document(collection = "achievements")
public class Achievement {
	@Id
	private ObjectId id;
	private ObjectId userId;
	private ObjectId skillId;
	private String title;
	private String description;
	private List<Achievement> prerequisites;
	private boolean isComplete;
	private Instant completedAt;
	@CreatedDate
	private Instant createdAt;
	
	public Achievement() {}

	public Achievement(@JsonProperty("_id") ObjectId id,
					   @JsonProperty("user_id") ObjectId userId,
					   @JsonProperty("skill_id") ObjectId skillId,
					   @JsonProperty("title") String title,
					   @JsonProperty("description") String description,
					   @JsonProperty("prerequisites") List<Achievement> prerequisites,
					   @JsonProperty("is_complete") boolean isComplete,
					   @JsonProperty("completed_at") Instant completedAt,
					   @JsonProperty("created_at") Instant createdAt) {
		this.id = id;
		this.userId = userId;
		this.skillId = skillId;
		this.title = title;
		this.description = description;
		this.prerequisites = prerequisites != null ? prerequisites : new ArrayList<Achievement>();
		this.isComplete = isComplete;
		this.completedAt = completedAt;
		this.createdAt = createdAt;
	}

	public ObjectId getId() {
		return id;
	}

	public void setId(ObjectId id) {
		this.id = id;
	}

	public ObjectId getUserId() {
		return userId;
	}

	public void setUserId(ObjectId userId) {
		this.userId = userId;
	}

	public ObjectId getSkillId() {
		return skillId;
	}

	public void setSkillId(ObjectId skillId) {
		this.skillId = skillId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public List<Achievement> getPrerequisites() {
		return prerequisites;
	}

	public void setPrerequisites(List<Achievement> prerequisites) {
		this.prerequisites = prerequisites;
	}

	public boolean isComplete() {
		return isComplete;
	}

	public void setComplete(boolean isComplete) {
		this.isComplete = isComplete;
	}

	public Instant getCompletedAt() {
		return completedAt;
	}

	public void setCompletedAt(Instant completedAt) {
		this.completedAt = completedAt;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}
	
}
