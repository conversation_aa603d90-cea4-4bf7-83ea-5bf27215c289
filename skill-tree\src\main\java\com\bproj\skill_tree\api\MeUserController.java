package com.bproj.skill_tree.api;

import org.bson.types.ObjectId;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.time.Instant;

import com.bproj.skill_tree.model.User;
import com.bproj.skill_tree.service.UserService;

import java.util.NoSuchElementException;
import java.util.Optional;

@RestController
@RequestMapping("/api/me/users")
public class MeUserController {
	private final UserService userService;
	
	public MeUserController(UserService userService) {
		this.userService = userService;
	}
	
	// Create a new User under this FirebaseId
	@PostMapping
	public ResponseEntity<User> create(Authentication auth) {
		User user = new User(ObjectId.get(),
						   auth.getPrincipal().toString(),
						   "",
						   "",
						   Instant.now(),
						   Instant.now());
		User createdUser = userService.create(user);
		return createdUser != null
			? new ResponseEntity<>(createdUser, HttpStatus.CREATED)
			: new ResponseEntity<>(HttpStatus.BAD_REQUEST);
	}
	
	// Return this User
	@GetMapping
	public ResponseEntity<User> findById(Authentication auth) {
		try {
			return userService.findByFirebaseId(auth.getPrincipal().toString())
				.map(user -> new ResponseEntity<>(user, HttpStatus.OK))
				.orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
	
	// Update this user
	@PutMapping
	public ResponseEntity<User> update(Authentication auth, @RequestBody User updatedUser) {
		if (auth.getPrincipal().toString().equals(updatedUser.getFirebaseId())) {
			User result = userService.update(updatedUser);
			return new ResponseEntity<>(result, HttpStatus.OK);
		}
		return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
	}
	
	// Delete this user
	@DeleteMapping
	public ResponseEntity<Void> deleteById(Authentication auth) {
		Optional<User> query = userService.findByFirebaseId(auth.getPrincipal().toString());
		try {
			User user = query.get();
			userService.deleteById(user.getId());
			return new ResponseEntity<>(HttpStatus.OK);
		} catch(NoSuchElementException nsee) {
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		}
	}
}
