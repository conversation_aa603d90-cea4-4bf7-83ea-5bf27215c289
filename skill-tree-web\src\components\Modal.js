import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

export default function Modal({ open, title, onClose, children }) {
    const dialogRef = useRef(null);

    useEffect(() => {
        if (open && dialogRef.current) {
            dialogRef.current.focus();
        }
    }, [open]);

    if (!open) return null;

    return createPortal(
        <div className="modal-backdrop" onMouseDown={onClose}>
            <div
                className="modal-window"
                role="dialog"
                aria-model="true"
                aria-labelledby="modal-title"
                tabIndex={-1}
                ref={dialogRef}
                onMouseDown={(e) => e.stopPropagation()}
                onKeyDown={(e) => e.key === "Escape" && onClose()}
            >
                <div className="modal-header">
                    <h3 id="modal-title">{title}</h3>
                    <button className="modal-close" onClick={onClose} aria-label="Close">x</button>
                </div>
                <div className="modal-body">
                    {children}
                </div>
            </div>
        </div>,
        document.body
    );

}