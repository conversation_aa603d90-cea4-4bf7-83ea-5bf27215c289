package com.bproj.skill_tree.service;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.bproj.skill_tree.dao.UserRepository;
import com.bproj.skill_tree.model.User;

import java.util.List;
import java.util.Optional;

@Service
public class UserService {
	private final UserRepository userRepository;
	
	@Autowired
	public UserService(@Qualifier("mongoUserRepository") UserRepository userRepository) {
		this.userRepository = userRepository;
	}
	
	public User create(User user) {
		return userRepository.insert(user);
	}
	
	public boolean existsById(ObjectId userId) {
		return userRepository.existsById(userId);
	}
	
	public Optional<User> findById(ObjectId userId) {
		return userRepository.findById(userId);
	}
	
	public Optional<User> findByFirebaseId(String firebaseId) {
		return userRepository.findByFirebaseId(firebaseId);
	}
	
	public Optional<User> findByEmail(String email) {
		return userRepository.findByEmail(email);
	}
	
	public List<User> findAll() {
		return userRepository.findAll();
	}
	
	public User update(User updatedUser) {
		return userRepository.save(updatedUser);
	}
	
	public void deleteById(ObjectId userId) {
		userRepository.deleteById(userId);
	}
}
