import { api } from "@/api/client";

export const AchievementsService = {
    listBySkill: (skillId) => api.get(`/skills/${skillId}/achievements`).then((r) => r.data),
    create: (skillId, payload) => api.post(`/skills/${skillId}/achievements`, payload).then((r) => r.data),
    update: (id, payload) => api.patch(`/achievements/${id}`, payload).then((r) => r.data),
    remove: (id) => api.delete(`/achievements/${id}`).then((r) => r.data),
};
