package com.bproj.skill_tree.service;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.bproj.skill_tree.dao.AchievementRepository;
import com.bproj.skill_tree.model.Achievement;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Service
public class AchievementService {
	private final AchievementRepository achievementRepository;
	
	@Autowired
	public AchievementService(@Qualifier("mongoAchievementRepository") AchievementRepository achievementRepository) {
		this.achievementRepository = achievementRepository;
	}
	
	public Achievement create(Achievement achievement) {
		return achievementRepository.insert(achievement);
	}
	
	public boolean existsById(ObjectId achievementId) {
		return achievementRepository.existsById(achievementId);
	}
	
	public Optional<Achievement> findById(ObjectId achievementId) {
		return achievementRepository.findById(achievementId);
	}
	
	public List<Achievement> findAll() {
		return achievementRepository.findAll();
	}
	
	public List<Achievement> findByUserId(ObjectId userId) {
		return achievementRepository.findByUserId(userId);
	}
	
	public List<Achievement> findBySkillId(ObjectId skillId) {
		return achievementRepository.findBySkillId(skillId);
	}
	
	public List<Achievement> findByUserIdAndSkillId(ObjectId userId, ObjectId skillId) {
		return achievementRepository.findByUserIdAndSkillId(userId, skillId);
	}
	
	public List<Achievement> findCompletedByUserId(ObjectId userId) {
		return achievementRepository.findByUserIdAndIsComplete(userId, true);
	}
	
	public List<Achievement> findIncompleteByUserId(ObjectId userId) {
		return achievementRepository.findByUserIdAndIsComplete(userId, false);
	}
	
	public List<Achievement> findCompletedBySkillId(ObjectId skillId) {
		return achievementRepository.findBySkillIdAndIsComplete(skillId, true);
	}
	
	public List<Achievement> findIncompleteBySkillId(ObjectId skillId) {
		return achievementRepository.findBySkillIdAndIsComplete(skillId, false);
	}
	
	public List<Achievement> findByTitle(String title) {
		return achievementRepository.findByTitle(title);
	}
	
	public List<Achievement> findByUserIdAndTitle(ObjectId userId, String title) {
		return achievementRepository.findByUserIdAndTitle(userId, title);
	}
	
	public Achievement update(Achievement updatedAchievement) {
		return achievementRepository.save(updatedAchievement);
	}
	
	public Achievement markAsCompleted(ObjectId achievementId) {
		Optional<Achievement> achievementOpt = findById(achievementId);
		if (achievementOpt.isPresent()) {
			Achievement achievement = achievementOpt.get();
			achievement.setComplete(true);
			achievement.setCompletedAt(Instant.now());
			return update(achievement);
		}
		return null;
	}
	
	public Achievement markAsIncomplete(ObjectId achievementId) {
		Optional<Achievement> achievementOpt = findById(achievementId);
		if (achievementOpt.isPresent()) {
			Achievement achievement = achievementOpt.get();
			achievement.setComplete(false);
			achievement.setCompletedAt(null);
			return update(achievement);
		}
		return null;
	}
	
	public void deleteById(ObjectId achievementId) {
		achievementRepository.deleteById(achievementId);
	}
	
	public void deleteByUserId(ObjectId userId) {
		List<Achievement> userAchievements = findByUserId(userId);
		achievementRepository.deleteAll(userAchievements);
	}
	
	public void deleteBySkillId(ObjectId skillId) {
		List<Achievement> skillAchievements = findBySkillId(skillId);
		achievementRepository.deleteAll(skillAchievements);
	}
}