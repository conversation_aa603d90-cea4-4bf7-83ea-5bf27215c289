package com.bproj.skill_tree.api;

import org.bson.types.ObjectId;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bproj.skill_tree.model.Skill;
import com.bproj.skill_tree.service.SkillService;
import java.util.List;

@RestController
@RequestMapping("api/admin/skills")
public class AdminSkillController {
	private final SkillService skillService;
	
	public AdminSkillController(SkillService skillService) {
		this.skillService = skillService;
	}
	
	// find all skills
	@GetMapping
	public ResponseEntity<List<Skill>> findAll() {
		List<Skill> skills = skillService.findAll();
		return new ResponseEntity<>(skills, HttpStatus.OK);
	}
	
	// find skill by id
	@GetMapping("/{skillId}")
	public ResponseEntity<Skill> findById(@PathVariable String skillId) {
		try {
			ObjectId objectId = new ObjectId(skillId);
			return skillService.findById(objectId)
				.map(skill -> new ResponseEntity<>(skill, HttpStatus.OK))
				.orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
		} catch (IllegalArgumentException e) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}
}
