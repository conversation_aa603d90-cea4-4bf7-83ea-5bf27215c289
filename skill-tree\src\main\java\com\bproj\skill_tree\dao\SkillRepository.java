package com.bproj.skill_tree.dao;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.bproj.skill_tree.model.Skill;

@Repository("mongoSkillRepository")
public interface SkillRepository extends MongoRepository<Skill, ObjectId>{
    List<Skill> findByUserId(ObjectId userId);
    List<Skill> findByUserIdAndParentSkillId(ObjectId userId, ObjectId parentSkillId);
    List<Skill> findByUserIdAndParentSkillIdIsNull(ObjectId userId);
    List<Skill> findByName(String name);
    List<Skill> findByUserIdAndName(ObjectId userId, String name);
}