import { useState, useMemo, useEffect } from "react";
import "./CreateSkillModal.css";

export default function CreateSkillModal({
    isOpen,
    onClose,
    onSubmit,
    allSkills = [],            // ✅ default to [] so mapping/select value never sees undefined
}) {
    const initialForm = {
        name: "",
        isRoot: true,
        parents: [],
        timeSpent: 0,
        hasRating: false,
        ratingType: 0,
        ratings: [],
        rating: 0,
    };

    const [form, setForm] = useState({
        name: "",
        isRoot: true,
        parents: [],             // array of ids (strings)
        timeSpent: 0,
        hasRating: false,
        ratingType: 0,           // 0 = numeric, 1 = ranks
        ratings: [],             // ranks list when ratingType = 1
        rating: 0,               // numeric rating when ratingType = 0
    });

    // optional: if props change while modal is closed, reset when opening
    useEffect(() => {
        if (isOpen) {
            setForm((f) => ({ ...f })); // no-op to keep; or reinit if you want
        }
    }, [isOpen]);

    const [ranksText, setRanksText] = useState(""); // ✅ control ranks text input consistently

    const parentOptions = useMemo(() => allSkills, [allSkills]);
    const update = (patch) => setForm((f) => ({ ...f, ...patch }));

    const handleChange = (e) => {
        const { name, type, value, checked } = e.target;

        if (type === "checkbox") {
            if (name === "isRoot") {
                // ✅ keep parents as array; if becoming root, clear parents to []
                update({ isRoot: checked, parents: checked ? [] : form.parents });
                return;
            }
            if (name === "hasRating") {
                // ✅ never set ratingType to []/undefined; keep types stable
                update({
                    hasRating: checked,
                    ratingType: checked ? form.ratingType : 0,
                    ratings: checked ? form.ratings : [],
                    rating: checked ? form.rating : 0,
                });
                return;
            }
            update({ [name]: checked });
            return;
        }

        // numbers: keep numeric, but allow empty "" to avoid controlled→uncontrolled
        if (name === "timeSpent" || name === "rating") {
            // Allow clearing the field: "" stays "", otherwise Number(...)
            const next = value === "" ? "" : Number(value);
            update({ [name]: next });
            return;
        }

        update({ [name]: value ?? "" }); // ✅ ensure strings never become undefined
    };

    const handleParentsChange = (e) => {
        const selected = Array.from(e.target.selectedOptions).map((o) => o.value);
        update({ parents: selected }); // ✅ always an array
    };

    const handleRatingTypeChange = (e) => {
        const v = Number(e.target.value);   // "0"/"1" -> number
        if (v === 0) {
            update({ ratingType: 0, ratings: [], rating: form.rating ?? 0 });
            setRanksText("");
        } else {
            update({ ratingType: 1, rating: 0 });
        }
    };

    const handleRanksInput = (e) => {
        const text = e.target.value;
        setRanksText(text);
        const arr = text.split(",").map((s) => s.trim()).filter(Boolean);
        update({ ratings: arr });
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        if (!form.isRoot && form.parents.length === 0) {
            alert("Please select at least one parent (this skill is not a root).");
            return;
        }

        if (form.hasRating) {
            if (form.ratingType === 0) {
                if (form.rating === "" || isNaN(form.rating)) {
                    alert("Please enter a numeric rating.");
                    return;
                }
            } else if (form.ratings.length === 0) {
                alert("Please provide at least one rank (comma-separated).");
                return;
            }
        }

        // Normalize numeric blanks before submit
        const payload = {
            ...form,
            rating: form.rating === "" ? 0 : form.rating,
            timeSpent: form.timeSpent === "" ? 0 : form.timeSpent,
        };

        onSubmit(payload);
        setForm(initialForm);
        setRanksText("");
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="modal-backdrop">
            <div className="modal">
                <h2>Create Skill</h2>
                <form onSubmit={handleSubmit}>
                    <label>
                        Skill name
                        <input
                            name="name"
                            value={form.name}               // ✅ always a string
                            onChange={handleChange}
                            placeholder="e.g., Algorithms"
                            required
                        />
                    </label>

                    <label style={{ display: "flex", alignItems: "center", gap: 8 }}>
                        <input
                            type="checkbox"
                            name="isRoot"
                            checked={form.isRoot}           // ✅ always boolean
                            onChange={handleChange}
                        />
                        Root skill?
                    </label>

                    {!form.isRoot && (
                        <label>
                            Parents (choose one or more)
                            <select
                                multiple
                                value={form.parents}           // ✅ always an array
                                onChange={handleParentsChange}
                                required
                            >
                                {parentOptions.map((s) => (
                                    <option key={s.id} value={s.id}>
                                        {s.name}
                                    </option>
                                ))};
                            </select>
                            <small>Hold Ctrl/Cmd to select multiple.</small>
                        </label>
                    )}

                    <label style={{ display: "flex", alignItems: "center", gap: 8 }}>
                        <input
                            type="checkbox"
                            name="hasRating"
                            checked={form.hasRating}
                            onChange={handleChange}
                        />
                        Use rating?
                    </label>

                    {form.hasRating && (
                        <>
                            <label>
                                Rating type
                                <select
                                    name="ratingType"
                                    value={String(form.ratingType)}
                                    onChange={handleRatingTypeChange}
                                    required
                                >
                                    <option value="0">Numeric Rating</option>
                                    <option value="1">Ranks (comma-separated)</option>
                                </select>
                            </label>

                            {form.ratingType === 0 ? (
                                <label>
                                    Rating value
                                    <input
                                        type="number"
                                        name="rating"
                                        value={form.rating}             // ✅ may be "" while editing
                                        onChange={handleChange}
                                        step="1"
                                        min="0"
                                    />
                                </label>
                            ) : (
                                <label>
                                    Rank options
                                    <input
                                        type="text"
                                        value={ranksText}               // ✅ controlled text input
                                        onChange={handleRanksInput}
                                        placeholder="e.g., Novice, Intermediate, Advanced, Expert"
                                    />
                                    <small>Separate ranks with commas.</small>
                                </label>
                            )}
                        </>
                    )}
                    <label>
                        Time spent (hours)
                        <input
                            type="number"
                            name="timeSpent"
                            value={form.timeSpent}
                            onChange={handleChange}
                            min="0"
                        />
                    </label>

                    <div className="actions">
                        <button type="button" onClick={onClose}>Cancel</button>
                        <button type="submit">Create</button>
                    </div>
                </form>
            </div>
        </div>
    );
}
